#!/usr/bin/env python3
"""
下载ZXing-cpp头文件
由于编译问题，这个脚本只下载头文件，库文件需要通过其他方式获取
"""

import os
import sys
import shutil
import urllib.request
import zipfile
from pathlib import Path

def download_file(url, filename, chunk_size=8192):
    """下载文件并显示进度"""
    print(f"正在下载: {url}")
    
    try:
        with urllib.request.urlopen(url) as response:
            total_size = int(response.headers.get('Content-Length', 0))
            downloaded = 0
            
            with open(filename, 'wb') as f:
                while True:
                    chunk = response.read(chunk_size)
                    if not chunk:
                        break
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r下载进度: {progress:.1f}%", end="", flush=True)
            
            print(f"\n下载完成: {filename}")
            return True
            
    except Exception as e:
        print(f"\n下载失败: {e}")
        return False

def extract_zip(zip_path, extract_to):
    """解压ZIP文件"""
    print(f"正在解压: {zip_path}")
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        print("解压完成")
        return True
    except Exception as e:
        print(f"解压失败: {e}")
        return False

def setup_zxing_headers():
    """设置ZXing头文件"""
    # 获取项目根目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    third_party_dir = os.path.join(project_root, "third_party")
    
    print("=== ZXing-cpp 头文件安装程序 ===")
    print(f"项目根目录: {project_root}")
    
    # 创建third_party目录
    os.makedirs(third_party_dir, exist_ok=True)
    
    # 下载ZXing源码
    zxing_url = "https://github.com/zxing-cpp/zxing-cpp/archive/refs/tags/v2.3.0.zip"
    zip_file = os.path.join(third_party_dir, "zxing-cpp-2.3.0.zip")
    
    print("下载ZXing-cpp源码...")
    if not download_file(zxing_url, zip_file):
        return False
    
    if not extract_zip(zip_file, third_party_dir):
        return False
    
    # 设置目录路径
    zxing_source_dir = os.path.join(third_party_dir, "zxing-cpp-2.3.0")
    zxing_final_dir = os.path.join(third_party_dir, "zxing")
    
    # 创建最终目录结构
    os.makedirs(zxing_final_dir, exist_ok=True)
    include_dir = os.path.join(zxing_final_dir, "include")
    lib_dir = os.path.join(zxing_final_dir, "lib")
    bin_dir = os.path.join(zxing_final_dir, "bin")
    
    os.makedirs(include_dir, exist_ok=True)
    os.makedirs(lib_dir, exist_ok=True)
    os.makedirs(bin_dir, exist_ok=True)
    
    # 复制头文件
    src_include = os.path.join(zxing_source_dir, "core", "src")
    dst_include = os.path.join(include_dir, "ZXing")
    
    if os.path.exists(src_include):
        shutil.copytree(src_include, dst_include, dirs_exist_ok=True)
        print(f"复制头文件: {src_include} -> {dst_include}")
    
    # 创建说明文件
    readme_content = """# ZXing-cpp 库文件说明

头文件已安装，但需要库文件才能编译。

## 获取库文件的方法：

### 方法1: 使用vcpkg (推荐)
```bash
# 安装vcpkg
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\\bootstrap-vcpkg.bat

# 安装ZXing-cpp
.\\vcpkg install zxing-cpp:x64-windows

# 复制库文件到这个目录
# 从 vcpkg\\installed\\x64-windows\\lib\\ 复制 *.lib 文件到 lib/
# 从 vcpkg\\installed\\x64-windows\\bin\\ 复制 *.dll 文件到 bin/
```

### 方法2: 下载预编译版本
访问: https://github.com/zxing-cpp/zxing-cpp/releases
下载适合您平台的预编译版本

### 方法3: 手动编译 (需要解决编码问题)
如果您想手动编译，需要先解决源码中的字符编码问题。

## 当前状态:
- ✅ 头文件已安装
- ❌ 库文件缺失 - 请使用上述方法之一获取

## 需要的文件:
- lib/ZXing.lib (Windows MSVC)
- bin/ZXing.dll (Windows)
"""
    
    with open(os.path.join(zxing_final_dir, "README.md"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    # 清理临时文件
    os.remove(zip_file)
    shutil.rmtree(zxing_source_dir)
    
    print(f"\n✅ ZXing-cpp头文件安装完成!")
    print(f"安装位置: {zxing_final_dir}")
    print("\n⚠️  注意: 还需要安装库文件才能编译")
    print("请查看 third_party/zxing/README.md 了解如何获取库文件")
    
    return True

def main():
    if setup_zxing_headers():
        print("\n🎉 头文件安装成功!")
        print("下一步: 请按照说明安装库文件")
    else:
        print("\n❌ 安装失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
